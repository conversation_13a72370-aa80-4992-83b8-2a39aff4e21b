const Payment = require('../models/Payment');
const Booking = require('../models/Booking');
const TransactionHistory = require('../models/TransactionHistory');
const BarberEarnings = require('../models/BarberEarnings');
const paymentConfig = require('../config/payment');
const axios = require('axios');
const crypto = require('crypto');
const notificationService = require('./notificationService');

class PaymentService {
  constructor() {
    // Determine environment and set correct base URL
    const environment = process.env.NODE_ENV || 'development';
    const isProduction = environment === 'production';
    
    // Use correct Monnify base URLs based on environment
    if (isProduction) {
      this.monnifyBaseUrl = process.env.MONIFY_BASE_URL || 'https://api.monnify.com';
    } else {
      this.monnifyBaseUrl = process.env.MONIFY_BASE_URL || 'https://sandbox.monnify.com';
    }
    
    // Check for both Monnify and Monify configurations
    this.monnifySecretKey = process.env.MONNIFY_SECRET_KEY || process.env.MONIFY_SECRET_KEY;
    this.monnifyApiKey = process.env.MONNIFY_API_KEY || process.env.MONIFY_API_KEY;
    this.monnifyContractCode = process.env.MONNIFY_CONTRACT_CODE || process.env.MONIFY_MERCHANT_ID;
    this.accessToken = null;
    this.tokenExpiresAt = null;
    
    console.log('PaymentService initialized with:', {
      environment,
      isProduction,
      baseUrl: this.monnifyBaseUrl,
      hasApiKey: !!this.monnifyApiKey,
      hasSecretKey: !!this.monnifySecretKey,
      hasContractCode: !!this.monnifyContractCode
    });
    
    // Validate required configuration
    if (!this.monnifyApiKey) {
      console.error('Missing MONNIFY_API_KEY or MONIFY_API_KEY in environment variables');
    }
    if (!this.monnifySecretKey) {
      console.error('Missing MONNIFY_SECRET_KEY or MONIFY_SECRET_KEY in environment variables');
    }
    if (!this.monnifyContractCode) {
      console.error('Missing MONNIFY_CONTRACT_CODE or MONIFY_MERCHANT_ID in environment variables');
    }
  }

  // Generate access token using Monnify authentication
  async getAccessToken() {
    try {
      // Validate credentials before making request
      if (!this.monnifyApiKey || !this.monnifySecretKey) {
        throw new Error('Missing Monnify API credentials. Please check your environment variables.');
      }

      // Check if we have a valid token
      if (this.accessToken && this.tokenExpiresAt && new Date() < this.tokenExpiresAt) {
        return this.accessToken;
      }

      // Create basic auth string
      const credentials = Buffer.from(`${this.monnifyApiKey}:${this.monnifySecretKey}`).toString('base64');
      
      console.log('Attempting Monnify authentication with:', {
        baseUrl: this.monnifyBaseUrl,
        apiKeyExists: !!this.monnifyApiKey,
        secretKeyExists: !!this.monnifySecretKey,
        apiKeyPrefix: this.monnifyApiKey ? this.monnifyApiKey.substring(0, 8) + '...' : 'N/A'
      });
      
      const response = await axios.post(
        `${this.monnifyBaseUrl}/api/v1/auth/login`,
        {},
        {
          headers: {
            'Authorization': `Basic ${credentials}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000 // 10 second timeout
        }
      );

      console.log('Monnify auth response status:', response.status);
      console.log('Monnify auth response success:', response.data?.requestSuccessful);

      if (response.data.requestSuccessful) {
        this.accessToken = response.data.responseBody.accessToken;
        // Set expiry time (subtract 60 seconds for safety)
        this.tokenExpiresAt = new Date(Date.now() + (response.data.responseBody.expiresIn - 60) * 1000);
        console.log('Monnify authentication successful');
        return this.accessToken;
      } else {
        throw new Error(response.data.responseMessage || 'Failed to authenticate with Monnify');
      }
    } catch (error) {
      console.error('Monnify authentication error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        baseUrl: this.monnifyBaseUrl,
        hasApiKey: !!this.monnifyApiKey,
        hasSecretKey: !!this.monnifySecretKey
      });
      throw new Error(`Failed to authenticate with Monnify: ${error.message}`);
    }
  }

  // Generate unique payment reference
  generatePaymentRef() {
    return `ETCH-${Date.now()}-${crypto.randomBytes(6).toString('hex')}`;
  }

  // Initialize transaction with Monnify
  async initializeTransaction(bookingData) {
    try {
      const { amount, userId, barberId, bookingId, customerEmail, customerName } = bookingData;
      
      const accessToken = await this.getAccessToken();
      const paymentReference = this.generatePaymentRef();
      
      const transactionData = {
        amount: parseFloat(amount),
        customerName: customerName || 'Customer',
        customerEmail: customerEmail,
        paymentReference,
        paymentDescription: `Booking payment for service - ${bookingId}`,
        currencyCode: 'NGN',
        contractCode: this.monnifyContractCode,
        paymentMethods: ['CARD', 'ACCOUNT_TRANSFER'],
        redirectUrl: `${process.env.CLIENT_URL}/dashboard/barbers/${barberId}/book?reference=${paymentReference}&status=verifying`,
        metaData: {
          bookingId,
          userId,
          barberId
        }
      };

      console.log('Initializing Monnify transaction:', transactionData);

      const response = await axios.post(
        `${this.monnifyBaseUrl}/api/v1/merchant/transactions/init-transaction`,
        transactionData,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Monnify response:', response.data);

      if (response.data.requestSuccessful) {
        const { transactionReference, checkoutUrl } = response.data.responseBody;
        
        // Create payment record
        const payment = new Payment({
          booking: bookingId,
          user: userId,
          barber: barberId,
          transactionReference,
          paymentReference,
          amount: parseFloat(amount),
          paymentMethod: 'monnify',
          status: 'pending',
          checkoutUrl
        });

        // Calculate amounts including platform commission
        payment.calculateAmounts(parseFloat(amount));

        // Set release timeline
        payment.releaseEligibleAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
        payment.autoReleaseAt = new Date(Date.now() + 72 * 60 * 60 * 1000); // 72 hours

        await payment.save();

        // Create transaction history record
        const transactionHistory = new TransactionHistory({
          user: userId,
          barber: barberId,
          booking: bookingId,
          amount: parseFloat(amount),
          transactionId: transactionReference,
          transactionReference,
          paymentReference,
          status: 'pending',
          paymentMethod: 'monnify',
          isHeldInEscrow: false,
          commissionPercentage: paymentConfig.platformCommission // Use commission from config
        });

        // Calculate commission and barber amount
        transactionHistory.calculateAmounts();
        await transactionHistory.save();

        return {
          paymentId: payment._id,
          transactionReference,
          paymentReference,
          checkoutUrl,
          amount: parseFloat(amount)
        };
      } else {
        throw new Error(response.data.responseMessage || 'Failed to initialize transaction');
      }
    } catch (error) {
      console.error('Transaction initialization error:', error.response?.data || error.message);
      throw new Error(`Payment initialization failed: ${error.message}`);
    }
  }

  // Verify transaction status with Monnify
  async verifyTransaction(transactionReference) {
    try {
      const accessToken = await this.getAccessToken();
      
      const encodedRef = encodeURIComponent(transactionReference);
      
      const response = await axios.get(
        `${this.monnifyBaseUrl}/api/v2/transactions/${encodedRef}`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.requestSuccessful) {
        return response.data.responseBody;
      } else {
        throw new Error(response.data.responseMessage || 'Failed to verify transaction');
      }
    } catch (error) {
      console.error('Transaction verification error:', error.response?.data || error.message);
      console.error('Error status:', error.response?.status);
      console.error('Error response message:', error.response?.data?.responseMessage);

      // Handle specific error cases
      if (error.response?.status === 404 ||
          error.response?.data?.responseMessage?.includes('no transaction matching')) {
        // Transaction doesn't exist - likely cancelled before creation
        console.log('Transaction not found - returning NOT_FOUND status');
        return {
          paymentStatus: 'NOT_FOUND',
          transactionReference: transactionReference,
          message: 'Transaction not found - likely cancelled before completion'
        };
      }
      
      throw new Error(`Transaction verification failed: ${error.message}`);
    }
  }

  // Release payment to barber
  async releasePayment(paymentId) {
    try {
      const payment = await Payment.findById(paymentId)
        .populate('booking')
        .populate('barber')
        .populate('user');
      
      if (!payment) {
        throw new Error('Payment not found');
      }

      if (payment.status !== 'held') {
        throw new Error('Payment is not in held status');
      }

      // For now, we'll mark as released since Monnify doesn't have direct disbursement in sandbox
      // In production, you would use the disbursement API
      payment.status = 'released';
      payment.releasedAt = new Date();
      await payment.save();

      // Update transaction history
      await TransactionHistory.findOneAndUpdate(
        { transactionReference: payment.transactionReference },
        { 
          status: 'success',
          isHeldInEscrow: false
        }
      );

      // Release pending balance to available balance for barber
      const barberEarnings = await BarberEarnings.getOrCreate(payment.barber._id);
      
      // Check if pending balance exists and matches the payment amount
      if (barberEarnings.pendingBalance < payment.barberAmount) {
        // If pending balance is insufficient, add the payment to pending first
        console.log(`Adding payment to pending balance: ${payment.barberAmount}`);
        await barberEarnings.handleServiceCompletion(payment);
      }
      
      // Now release the pending balance
      await barberEarnings.releasePendingBalance(payment);

      // Send credit notification to barber (handled in the payment controller)
      console.log('Payment released successfully, notification will be sent from payment controller');

      return payment;
    } catch (error) {
      throw new Error(`Payment release failed: ${error.message}`);
    }
  }

  // Handle webhook from Monnify
  async handleWebhook(eventData) {
    try {
      const { eventType, eventData: data } = eventData;
      
      console.log('Processing Monnify webhook:', { eventType, data });

      switch (eventType) {
        case 'SUCCESSFUL_TRANSACTION':
          await this.handlePaymentSuccess(data);
          break;
        case 'FAILED_TRANSACTION':
          await this.handlePaymentFailure(data);
          break;
        default:
          console.log('Unhandled webhook event type:', eventType);
      }
    } catch (error) {
      console.error('Webhook processing error:', error);
      throw error;
    }
  }

  // Handle successful payment
  async handlePaymentSuccess(data) {
    try {
      const { paymentReference, transactionReference, amountPaid } = data;
      
      const payment = await Payment.findOne({ 
        $or: [
          { paymentReference },
          { transactionReference }
        ]
      }).populate('booking user barber');

      if (!payment) {
        console.error('Payment not found for reference:', { paymentReference, transactionReference });
        return;
      }

      // Update payment status
      payment.status = 'held'; // Hold in escrow
      payment.paidAt = new Date();
      payment.webhookData = data;
      await payment.save();

      // Update booking status
      if (payment.booking) {
        await Booking.findByIdAndUpdate(payment.booking._id, {
          status: 'confirmed',
          paymentStatus: 'completed'
        });
      }

      // Update transaction history
      await TransactionHistory.findOneAndUpdate(
        { transactionReference },
        { 
          status: 'success',
          isHeldInEscrow: true,
          paymentDetails: data
        }
      );

      // Update barber earnings - add to pending balance
      const barberEarnings = await BarberEarnings.getOrCreate(payment.barber._id);
      await barberEarnings.handleServiceCompletion(payment);

      // Send notifications
      await notificationService.sendPaymentConfirmation(payment);

      return payment;
    } catch (error) {
      console.error('Payment success handling error:', error);
      throw error;
    }
  }

  // Handle failed payment
  async handlePaymentFailure(data) {
    try {
      const { paymentReference, transactionReference, paymentStatus } = data;
      
      const payment = await Payment.findOne({ 
        $or: [
          { paymentReference },
          { transactionReference }
        ]
      }).populate('booking user barber');

      if (!payment) {
        console.error('Payment not found for reference:', { paymentReference, transactionReference });
        return;
      }

      // Determine if this is a cancellation or failure
      const isCancelled = paymentStatus === 'CANCELLED' || paymentStatus === 'canceled';
      const finalStatus = isCancelled ? 'cancelled' : 'failed';
      
      console.log('Processing payment failure/cancellation:', {
        paymentReference,
        paymentStatus,
        finalStatus
      });

      // Update payment status
      payment.status = finalStatus;
      payment.webhookData = data;
      await payment.save();

      // Update associated booking status
      if (payment.booking) {
        const Booking = require('../models/Booking');
        await Booking.findByIdAndUpdate(payment.booking._id, {
          status: 'cancelled',
          cancellationReason: isCancelled ? 'Payment cancelled by user' : 'Payment failed',
          paymentStatus: finalStatus,
          cancelledAt: new Date()
        });
      }

      // Update transaction history with correct status
      await TransactionHistory.findOneAndUpdate(
        { transactionReference },
        { 
          status: finalStatus, // Use 'cancelled' or 'failed' as appropriate
          paymentDetails: data
        }
      );

      console.log('Successfully updated payment, booking, and transaction history to status:', finalStatus);

      // Send failure notification
      await notificationService.sendPaymentFailureNotification(payment);

      return payment;
    } catch (error) {
      console.error('Payment failure handling error:', error);
      throw error;
    }
  }

  // Handle payment cancellation
  async handlePaymentCancellation(paymentReference) {
    try {
      const payment = await Payment.findOne({ 
        $or: [
          { paymentReference },
          { transactionReference: paymentReference }
        ]
      }).populate('booking user barber');

      if (!payment) {
        throw new Error('Payment not found');
      }

      // Only allow cancellation of pending payments
      if (payment.status !== 'pending') {
        throw new Error(`Cannot cancel payment with status: ${payment.status}`);
      }

      // Update payment status
      payment.status = 'cancelled';
      payment.cancelledAt = new Date();
      await payment.save();

      // Update associated booking status
      if (payment.booking) {
        const Booking = require('../models/Booking');
        await Booking.findByIdAndUpdate(payment.booking._id, {
          status: 'cancelled',
          cancellationReason: 'Payment cancelled by user',
          paymentStatus: 'cancelled',
          cancelledAt: new Date()
        });
      }

      // Update transaction history
      await TransactionHistory.findOneAndUpdate(
        { transactionReference: payment.transactionReference },
        { status: 'cancelled' }
      );

      return payment;
    } catch (error) {
      console.error('Payment cancellation error:', error);
      throw error;
    }
  }

  // Check and process auto-release for eligible payments
  async processAutoReleases() {
    try {
      const eligiblePayments = await Payment.find({
        status: 'held',
        autoReleaseAt: { $lte: new Date() }
      });

      for (const payment of eligiblePayments) {
        await this.releasePayment(payment._id);
      }

      return eligiblePayments.length;
    } catch (error) {
      console.error('Auto-release processing error:', error);
      throw error;
    }
  }

  // Initialize subscription transaction with Monnify
  async initializeSubscriptionTransaction(subscriptionData) {
    try {
      const { barberId, customerEmail, customerName } = subscriptionData;

      const accessToken = await this.getAccessToken();
      const paymentReference = this.generatePaymentRef();

      const transactionData = {
        amount: paymentConfig.subscriptionFee, // Use subscription fee from config
        customerName: customerName || 'Barber',
        customerEmail: customerEmail,
        paymentReference,
        paymentDescription: `Monthly subscription payment for barber - ${barberId}`,
        currencyCode: 'NGN',
        contractCode: this.monnifyContractCode,
        paymentMethods: ['CARD', 'ACCOUNT_TRANSFER'], // Match booking payment methods
        redirectUrl: `${process.env.CLIENT_URL}/barber/subscription?reference=${paymentReference}&status=verifying`,
        metaData: {
          barberId,
          type: 'subscription'
        },
        // Enable card tokenization for recurring payments
        tokenizeCard: true
      };

      console.log('Initializing Monnify subscription transaction with tokenization:', transactionData);

      const response = await axios.post(
        `${this.monnifyBaseUrl}/api/v1/merchant/transactions/init-transaction`,
        transactionData,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Monnify subscription response:', response.data);

      if (response.data.requestSuccessful) {
        const { transactionReference, checkoutUrl } = response.data.responseBody;
        
        // Set subscription status to pending
        const Subscription = require('../models/Subscription');
        const subscription = await Subscription.getOrCreate(barberId);
        subscription.status = 'pending';
        await subscription.save();
        
        return {
          transactionReference,
          paymentReference,
          checkoutUrl,
          amount: paymentConfig.subscriptionFee,
          subscriptionId: barberId
        };
      } else {
        throw new Error(response.data.responseMessage || 'Failed to initialize subscription transaction');
      }
    } catch (error) {
      console.error('Subscription transaction initialization error:', error.response?.data || error.message);
      throw new Error(`Subscription payment initialization failed: ${error.message}`);
    }
  }

  // Charge card token for recurring subscription payment
  async chargeCardToken(cardToken, amount, barberId, customerEmail) {
    try {
      const accessToken = await this.getAccessToken();
      const paymentReference = this.generatePaymentRef();
      
      const chargeData = {
        amount: amount,
        paymentReference,
        paymentDescription: `Recurring subscription payment for barber - ${barberId}`,
        currencyCode: 'NGN',
        contractCode: this.monnifyContractCode,
        cardToken: cardToken,
        customerEmail: customerEmail,
        metaData: {
          barberId,
          type: 'recurring_subscription',
          isRecurring: true
        }
      };

      console.log('Charging card token for recurring payment:', {
        paymentReference,
        amount,
        barberId,
        customerEmail
      });

      const response = await axios.post(
        `${this.monnifyBaseUrl}/api/v1/merchant/transactions/charge-card-token`,
        chargeData,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Card token charge response:', response.data);

      if (response.data.requestSuccessful) {
        const { transactionReference, paymentStatus } = response.data.responseBody;
        
        return {
          success: true,
          transactionReference,
          paymentReference,
          paymentStatus,
          amount,
          barberId
        };
      } else {
        throw new Error(response.data.responseMessage || 'Failed to charge card token');
      }
    } catch (error) {
      console.error('Card token charge error:', error.response?.data || error.message);
      return {
        success: false,
        error: error.message,
        barberId
      };
    }
  }

  // Get payment status
  async getPaymentStatus(paymentId) {
    try {
      const payment = await Payment.findById(paymentId)
        .populate('booking user barber');
      
      if (!payment) {
        throw new Error('Payment not found');
      }

      // Verify with Monnify if still pending
      if (payment.status === 'pending' && payment.transactionReference) {
        try {
          const monnifyData = await this.verifyTransaction(payment.transactionReference);
          
          if (monnifyData.paymentStatus === 'PAID') {
            await this.handlePaymentSuccess(monnifyData);
            
            // Refresh payment data after handling success
            return await Payment.findById(paymentId)
              .populate('booking user barber');
          } else if (monnifyData.paymentStatus === 'FAILED') {
            await this.handlePaymentFailure(monnifyData);
            
            // Refresh payment data after handling failure
            return await Payment.findById(paymentId)
              .populate('booking user barber');
          }
        } catch (verifyError) {
          console.error('Payment verification error:', verifyError);
        }
      }

      return payment;
    } catch (error) {
      throw new Error(`Failed to get payment status: ${error.message}`);
    }
  }
  
  // Get payment by reference
  async getPaymentByReference(reference) {
    try {
      const payment = await Payment.findOne({
        $or: [
          { paymentReference: reference },
          { transactionReference: reference }
        ]
      }).populate('booking user barber');
      
      return payment;
    } catch (error) {
      throw new Error(`Failed to get payment by reference: ${error.message}`);
    }
  }
  
  // Verify subscription payment status
  async verifySubscriptionPayment(paymentReference, barberId = null) {
    try {
      console.log('Verifying subscription payment with reference:', paymentReference);
      if (barberId) {
        console.log('For specific barber:', barberId);
      }
      
      // Basic validation of payment reference format
      if (!paymentReference || !paymentReference.startsWith('ETCH-')) {
        return {
          success: false,
          status: 'error',
          message: 'Invalid payment reference format'
        };
      }
      
      // First, try to find subscription by payment reference in metadata
      const Subscription = require('../models/Subscription');
      const subscription = await Subscription.findOne({
        'paymentHistory.paymentReference': paymentReference
      }).populate('barber');
      
      if (subscription) {
        console.log('Found subscription for payment reference - already processed');
        // Payment already processed, return the subscription status
        return {
          success: true,
          status: 'completed',
          data: {
            subscription: subscription,
            barber: subscription.barber
          }
        };
      }
      
      console.log('Payment not found in subscription history, verifying with Monnify...');
      
      // CRITICAL: Actually verify with Monnify before proceeding
      try {
        console.log('Calling Monnify verification for payment reference:', paymentReference);
        const monnifyData = await this.verifyTransaction(paymentReference);
        console.log('Monnify verification result:', JSON.stringify(monnifyData, null, 2));

        // Handle different payment statuses
        if (monnifyData.paymentStatus === 'NOT_FOUND') {
          console.log('Transaction not found in Monnify - likely cancelled before completion');
          return {
            success: false,
            status: 'cancelled',
            message: 'Payment was cancelled before completion'
          };
        } else if (monnifyData.paymentStatus !== 'PAID') {
          console.log('Monnify reports payment status as:', monnifyData.paymentStatus);
          console.log('Monnify response code:', monnifyData.responseCode);

          // Check for various cancellation indicators
          if (monnifyData.paymentStatus === 'CANCELLED' ||
              monnifyData.paymentStatus === 'USER_CANCELLED' ||
              monnifyData.responseCode === 'USER_CANCELLED') {
            return {
              success: false,
              status: 'cancelled',
              message: 'Payment was cancelled'
            };
          } else if (monnifyData.paymentStatus === 'FAILED') {
            return {
              success: false,
              status: 'failed',
              message: 'Payment failed'
            };
          } else if (monnifyData.paymentStatus === 'PENDING' || monnifyData.paymentStatus === 'PROCESSING') {
            return {
              success: false,
              status: 'pending',
              message: 'Payment is still being processed'
            };
          } else {
            // Unknown status - log it for debugging
            console.log('Unknown Monnify payment status:', monnifyData.paymentStatus, 'Response code:', monnifyData.responseCode);
            return {
              success: false,
              status: 'error',
              message: `Unknown payment status: ${monnifyData.paymentStatus}`
            };
          }
        }

        console.log('Monnify confirms payment is PAID, proceeding with subscription activation...');

        // Extract card token from the response if available
        // Note: The card token might be in different fields depending on Monnify's response structure
        const cardToken = monnifyData.cardToken || monnifyData.card?.token || monnifyData.tokenizedCard || null;
        console.log('Card token available:', !!cardToken);
        console.log('Monnify response structure for token extraction:', {
          hasCardToken: !!monnifyData.cardToken,
          hasCardObject: !!monnifyData.card,
          hasTokenizedCard: !!monnifyData.tokenizedCard,
          responseKeys: Object.keys(monnifyData)
        });

      } catch (verifyError) {
        console.error('Monnify verification failed:', verifyError);

        // If Monnify verification fails, it might be because the transaction was cancelled
        // before it was even created, or there's a network issue
        if (verifyError.response?.status === 404 ||
            verifyError.message?.includes('not found') ||
            verifyError.message?.includes('NOT_FOUND') ||
            verifyError.message?.includes('no transaction matching')) {
          return {
            success: false,
            status: 'cancelled',
            message: 'Payment was cancelled before completion'
          };
        }

        return {
          success: false,
          status: 'error',
          message: 'Failed to verify payment with payment provider'
        };
      }
      
      // Now find the subscription to activate
        let subscriptionToActivate = null;
        
        // If we have a barber ID from the authenticated request, use it directly
        if (barberId) {
          console.log('Looking for subscription for authenticated barber:', barberId);
          
          const barberSubscription = await Subscription.findOne({
            barber: barberId,
            status: { $in: ['never_subscribed', 'expired'] }
          }).populate('barber');
          
          if (barberSubscription) {
            subscriptionToActivate = barberSubscription;
            console.log('Found subscription for authenticated barber:', barberSubscription.barber.email);
          } else {
            console.log('No pending subscription found for authenticated barber');
            return {
              success: false,
              status: 'error',
              message: 'No pending subscription found for your account'
            };
          }
        } else {
          // Fallback to time-based approach if no barber ID provided
          console.log('No barber ID provided, using time-based approach');
          
          const timestamp = paymentReference.split('-')[1];
          const paymentTime = new Date(parseInt(timestamp));
          
          console.log('Payment timestamp:', paymentTime);
          
          const fiveMinutesBefore = new Date(paymentTime.getTime() - 5 * 60 * 1000);
          const fiveMinutesAfter = new Date(paymentTime.getTime() + 5 * 60 * 1000);
          
          const candidateSubscriptions = await Subscription.find({
            status: { $in: ['never_subscribed', 'expired'] },
            $or: [
              { updatedAt: { $gte: fiveMinutesBefore, $lte: fiveMinutesAfter } },
              { createdAt: { $gte: fiveMinutesBefore, $lte: fiveMinutesAfter } }
            ]
          }).populate('barber');
          
          console.log('Found candidate subscriptions by time:', candidateSubscriptions.length);
          
          if (candidateSubscriptions.length === 1) {
            subscriptionToActivate = candidateSubscriptions[0];
          } else if (candidateSubscriptions.length === 0) {
          return {
            success: false,
            status: 'error',
            message: 'No pending subscription found for this payment'
          };
          } else {
            return {
              success: false,
              status: 'error',
              message: 'Multiple pending subscriptions found, please contact support'
            };
          }
        }
        
        // If we found a subscription to activate, proceed with activation
        if (subscriptionToActivate) {
          console.log('Activating subscription for barber:', subscriptionToActivate.barber.email);
          
          // Activate subscription with payment data including card token
          subscriptionToActivate.activateSubscription({
            transactionReference: paymentReference,
            paymentReference: paymentReference,
            amount: subscriptionToActivate.subscriptionFee,
            paymentDate: new Date(),
            cardToken: cardToken,
            isRecurring: false // First payment is not recurring
          });
          
          await subscriptionToActivate.save();
          
          // Send subscription activation email
          const emailService = require('./emailService');
          try {
            console.log('Sending subscription activation email to:', subscriptionToActivate.barber.email);
            await emailService.sendSubscriptionActivationEmail(
              subscriptionToActivate.barber.email,
              subscriptionToActivate.barber.fullName,
              {
                amount: subscriptionToActivate.subscriptionFee,
                startDate: subscriptionToActivate.currentPeriod.startDate,
                endDate: subscriptionToActivate.currentPeriod.endDate,
                transactionReference: paymentReference
              }
            );
            console.log('Subscription activation email sent successfully');
          } catch (emailError) {
            console.error('Failed to send subscription activation email:', emailError);
          }
          
          return {
            success: true,
            status: 'completed',
            data: {
              subscription: subscriptionToActivate,
              barber: subscriptionToActivate.barber,
              payment: {
                status: 'completed',
                amount: subscriptionToActivate.subscriptionFee,
                paymentReference: paymentReference,
                transactionReference: paymentReference
              }
            }
          };
        } else {
          return {
            success: false,
            status: 'error',
            message: 'No pending subscription found for this payment'
          };
        }
      
    } catch (error) {
      console.error('Subscription payment verification error:', error);

      // Check if this is a cancellation-related error
      if (error.response?.status === 404 ||
          error.message?.includes('not found') ||
          error.message?.includes('NOT_FOUND') ||
          error.message?.includes('no transaction matching') ||
          error.message?.includes('cancelled')) {
        return {
          success: false,
          status: 'cancelled',
          message: 'Payment was cancelled'
        };
      }

      return {
        success: false,
        status: 'error',
        message: error.message || 'Failed to verify subscription payment'
      };
    }
  }
  
  // Cancel subscription payment
  async cancelSubscriptionPayment(paymentReference) {
    try {
      console.log('Cancelling subscription payment:', paymentReference);
      
      // For subscription payments, we don't need to do much cleanup
      // since the subscription wasn't activated yet
      return {
        success: true,
        message: 'Subscription payment cancelled successfully'
      };
    } catch (error) {
      console.error('Subscription payment cancellation error:', error);
      throw new Error(`Failed to cancel subscription payment: ${error.message}`);
    }
  }

  // Verify booking payment status (similar to subscription verification)
  async verifyBookingPayment(paymentReference, userId = null) {
    try {
      console.log('Verifying booking payment with reference:', paymentReference);
      if (userId) {
        console.log('For specific user:', userId);
      }

      // Basic validation of payment reference format
      if (!paymentReference || !paymentReference.startsWith('ETCH-')) {
        return {
          success: false,
          status: 'error',
          message: 'Invalid payment reference format'
        };
      }

      // Find payment by reference
      const payment = await this.getPaymentByReference(paymentReference);
      
      if (!payment) {
        return {
          success: false,
          status: 'error',
          message: 'Payment not found'
        };
      }

      // If user ID is provided, verify ownership
      if (userId && payment.user._id.toString() !== userId.toString()) {
        return {
          success: false,
          status: 'error',
          message: 'Not authorized to verify this payment'
        };
      }

      console.log('Current payment status in database:', payment.status);

      // If payment is already processed, return the current status
      if (payment.status !== 'pending') {
        const statusMap = {
          'held': 'success',
          'completed': 'success', 
          'paid': 'success',
          'released': 'success',
          'failed': 'failed',
          'cancelled': 'cancelled',
          'disputed': 'failed',
          'refunded': 'cancelled'
        };

        const mappedStatus = statusMap[payment.status] || payment.status;
        console.log('Payment already processed with status:', payment.status, '- mapped to:', mappedStatus);

        return {
          success: payment.status === 'held' || payment.status === 'completed' || payment.status === 'paid' || payment.status === 'released',
          status: mappedStatus,
          message: payment.status === 'failed' ? 'Payment failed' : 
                   payment.status === 'cancelled' ? 'Payment was cancelled' : undefined,
          data: {
            payment: {
              id: payment._id,
              status: payment.status,
              amount: payment.amount,
              paymentReference: payment.paymentReference,
              transactionReference: payment.transactionReference,
              paidAt: payment.paidAt
            },
            booking: payment.booking ? {
              id: payment.booking._id,
              status: payment.booking.status,
              paymentStatus: payment.booking.paymentStatus,
              date: payment.booking.date,
              startTime: payment.booking.startTime,
              endTime: payment.booking.endTime,
              totalPrice: payment.booking.totalPrice
            } : null
          }
        };
      }

      // Payment is still pending, verify with Monnify
      try {
        console.log('Payment is pending, verifying with Monnify...');
        const monnifyData = await this.verifyTransaction(payment.transactionReference);
        
        console.log('Monnify verification result:', monnifyData);

        if (monnifyData.paymentStatus === 'PAID') {
          // Payment successful - handle the success
          console.log('Monnify reports payment as PAID, processing success...');
          await this.handlePaymentSuccess({
            paymentReference: payment.paymentReference,
            transactionReference: payment.transactionReference,
            amountPaid: monnifyData.amountPaid,
            ...monnifyData
          });

          // Refresh payment data
          const updatedPayment = await this.getPaymentByReference(paymentReference);
          
          return {
            success: true,
            status: 'success',
            data: {
              payment: {
                id: updatedPayment._id,
                status: updatedPayment.status,
                amount: updatedPayment.amount,
                paymentReference: updatedPayment.paymentReference,
                transactionReference: updatedPayment.transactionReference,
                paidAt: updatedPayment.paidAt
              },
              booking: updatedPayment.booking ? {
                id: updatedPayment.booking._id,
                status: updatedPayment.booking.status,
                paymentStatus: updatedPayment.booking.paymentStatus,
                date: updatedPayment.booking.date,
                startTime: updatedPayment.booking.startTime,
                endTime: updatedPayment.booking.endTime,
                totalPrice: updatedPayment.booking.totalPrice
              } : null
            }
          };

        } else if (monnifyData.paymentStatus === 'FAILED' || monnifyData.paymentStatus === 'CANCELLED') {
          // Payment failed - handle the failure
          console.log('Monnify reports payment as', monnifyData.paymentStatus, '- processing failure...');
          await this.handlePaymentFailure({
            paymentReference: payment.paymentReference,
            transactionReference: payment.transactionReference,
            paymentStatus: monnifyData.paymentStatus,
            ...monnifyData
          });

          return {
            success: false,
            status: monnifyData.paymentStatus === 'CANCELLED' ? 'cancelled' : 'failed',
            message: monnifyData.paymentStatus === 'CANCELLED' 
              ? 'Payment was cancelled' 
              : 'Payment failed'
          };

        } else {
          // Payment is still pending with Monnify
          console.log('Monnify reports payment as still pending:', monnifyData.paymentStatus);
          return {
            success: false,
            status: 'pending',
            message: 'Payment is still being processed'
          };
        }

      } catch (verifyError) {
        console.error('Monnify verification error:', verifyError);
        
        // If verification fails, assume payment is still pending
        return {
          success: false,
          status: 'pending',
          message: 'Payment verification in progress'
        };
      }

    } catch (error) {
      console.error('Booking payment verification error:', error);
      return {
        success: false,
        status: 'error',
        message: error.message || 'Failed to verify booking payment'
      };
    }
  }
  
  // Get transaction history for a user
  async getUserTransactionHistory(userId) {
    try {
      return await TransactionHistory.find({ user: userId })
        .populate('barber', 'fullName businessName profileImage')
        .populate({
          path: 'booking',
          populate: {
            path: 'service',
            select: 'name price duration description'
          }
        })
        .sort('-createdAt');
    } catch (error) {
      throw new Error(`Failed to get user transaction history: ${error.message}`);
    }
  }
  
  // Get transaction history for a barber
  async getBarberTransactionHistory(barberId) {
    try {
      // Exclude cancelled transactions for barbers since they don't affect barber earnings
      // Cancelled transactions should only be visible to users who initiated the payment
      return await TransactionHistory.find({ 
        barber: barberId,
        status: { $ne: 'cancelled' } // Exclude cancelled transactions
      })
        .populate('user', 'fullName profileImage')
        .populate({
          path: 'booking',
          populate: {
            path: 'service',
            select: 'name price duration description'
          }
        })
        .sort('-createdAt');
    } catch (error) {
      throw new Error(`Failed to get barber transaction history: ${error.message}`);
    }
  }

  // Get list of Nigerian banks from Monnify
  async getBankList() {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await axios.get(
        `${this.monnifyBaseUrl}/api/v1/banks`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Monnify bank list response:', response.data);

      if (response.data.requestSuccessful) {
        return {
          success: true,
          banks: response.data.responseBody
        };
      } else {
        throw new Error(response.data.responseMessage || 'Failed to fetch bank list');
      }
    } catch (error) {
      console.error('Bank list fetch error:', error.response?.data || error.message);
      return {
        success: false,
        error: error.message,
        banks: []
      };
    }
  }

  // Verify bank account details using Monnify
  async verifyBankAccount(bankCode, accountNumber) {
    try {
      const accessToken = await this.getAccessToken();
      
      const response = await axios.get(
        `${this.monnifyBaseUrl}/api/v1/disbursements/account/validate`,
        {
          params: {
            bankCode,
            accountNumber
          },
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Monnify account verification response:', response.data);

      if (response.data.requestSuccessful) {
        return {
          success: true,
          accountName: response.data.responseBody.accountName,
          accountNumber: response.data.responseBody.accountNumber,
          bankCode: response.data.responseBody.bankCode
        };
      } else {
        return {
          success: false,
          error: response.data.responseMessage || 'Account verification failed'
        };
      }
    } catch (error) {
      console.error('Account verification error:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.responseMessage || error.message || 'Account verification failed'
      };
    }
  }
}

// Export the payment service instance
const paymentServiceInstance = new PaymentService();

// Export the validateBankAccount function for backward compatibility
const validateBankAccount = async (bankDetails) => {
  const { bankCode, accountNumber } = bankDetails;
  return await paymentServiceInstance.verifyBankAccount(bankCode, accountNumber);
};

module.exports = paymentServiceInstance;
module.exports.validateBankAccount = validateBankAccount; 