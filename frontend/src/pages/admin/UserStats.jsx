import React, { useState, useEffect } from 'react';
import { 
  FiUsers, 
  FiUserPlus, 
  FiUserCheck, 
  FiTrendingUp, 
  FiTrendingDown,
  FiBarChart, 
  FiActivity, 
  FiTarget,
  FiRefreshCw,
  FiAlertCircle,
  FiUserX,
  FiCalendar
} from 'react-icons/fi';
import adminService from '../../services/adminService';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { toast } from 'react-hot-toast';

const UserStats = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [userStats, setUserStats] = useState({
    totalUsers: 0,
    newUsersThisMonth: 0,
    newUsersLastMonth: 0,
    activeUsers: 0,
    userGrowthRate: 0,
    averageBookingsPerUser: 0,
    topUsers: [],
    userActivity: {
      daily: 0,
      weekly: 0,
      monthly: 0
    }
  });

  useEffect(() => {
    fetchUserStats();
  }, []);

  const fetchUserStats = async () => {
    try {
      setIsLoading(true);
      const response = await adminService.getUserStats();
      if (response.success) {
        setUserStats(response.data);
      }
    } catch (error) {
      console.error('Error fetching user stats:', error);
      toast.error('Failed to load user statistics');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshStats = async () => {
    setIsRefreshing(true);
    await fetchUserStats();
    setIsRefreshing(false);
    toast.success('Statistics updated successfully');
  };

  const formatNumber = (value) => {
    if (value === undefined || value === null) return '0';
    return value.toLocaleString();
  };

  const formatPercentage = (value) => {
    if (value === undefined || value === null) return '0%';
    const sign = value > 0 ? '+' : '';
    return `${sign}${Math.round(value)}%`;
  };

  const getGrowthColor = (value) => {
    if (value === undefined || value === null) return 'text-gray-600';
    if (value > 0) return 'text-emerald-600';
    if (value < 0) return 'text-rose-600';
    return 'text-gray-600';
  };

  const getGrowthIcon = (value) => {
    if (value === undefined || value === null) return <FiActivity className="h-4 w-4" />;
    if (value > 0) return <FiTrendingUp className="h-4 w-4" />;
    if (value < 0) return <FiTrendingDown className="h-4 w-4" />;
    return <FiActivity className="h-4 w-4" />;
  };

  const calculateGrowthRate = (current, previous) => {
    if (!previous || previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Loading user statistics...</p>
        </div>
      </div>
    );
  }

  // Ensure all required values have defaults
  const {
    totalUsers = 0,
    newUsersThisMonth = 0,
    newUsersLastMonth = 0,
    activeUsers = 0,
    userGrowthRate = 0,
    averageBookingsPerUser = 0,
    userActivity = {
      daily: 0,
      weekly: 0,
      monthly: 0
    }
  } = userStats || {};

  return (
    <div className="p-3 sm:p-4 max-w-full">
      {/* Header */}
      <div className="mb-4 sm:mb-6">
        <h1 className="text-lg sm:text-xl font-bold text-gray-900 mb-1">User Statistics</h1>
        <p className="text-xs sm:text-sm text-gray-600">Overview of user metrics and activity</p>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-4 sm:mb-6">
        {/* Total Users */}
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-3 sm:p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between mb-2 sm:mb-3">
            <div>
              <p className="text-xs sm:text-sm font-medium text-blue-900/70">Total Users</p>
              <p className="text-lg sm:text-xl lg:text-2xl font-bold text-blue-900 mt-1">{formatNumber(totalUsers)}</p>
            </div>
            <div className="p-2 bg-blue-200/50 rounded-lg">
              <FiUsers className="h-4 w-4 sm:h-5 sm:w-5 text-blue-700" />
            </div>
          </div>
          {userGrowthRate !== 0 && (
            <div className="flex items-center">
              <span className={`flex items-center text-xs font-medium ${getGrowthColor(userGrowthRate)}`}>
                {getGrowthIcon(userGrowthRate)}
                <span className="ml-1">{formatPercentage(userGrowthRate)}</span>
              </span>
              <span className="ml-2 text-xs text-gray-500">vs last month</span>
            </div>
          )}
        </div>

        {/* New Users */}
        <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 p-3 sm:p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between mb-2 sm:mb-3">
            <div>
              <p className="text-xs sm:text-sm font-medium text-emerald-900/70">New Users</p>
              <p className="text-lg sm:text-xl lg:text-2xl font-bold text-emerald-900 mt-1">{formatNumber(newUsersThisMonth)}</p>
            </div>
            <div className="p-2 bg-emerald-200/50 rounded-lg">
              <FiUserPlus className="h-4 w-4 sm:h-5 sm:w-5 text-emerald-700" />
            </div>
          </div>
          {newUsersLastMonth > 0 && (
            <div className="flex items-center">
              <span className={`flex items-center text-xs font-medium ${getGrowthColor(calculateGrowthRate(newUsersThisMonth, newUsersLastMonth))}`}>
                {getGrowthIcon(calculateGrowthRate(newUsersThisMonth, newUsersLastMonth))}
                <span className="ml-1">{formatNumber(newUsersLastMonth)}</span>
              </span>
              <span className="ml-2 text-xs text-gray-500">last month</span>
            </div>
          )}
        </div>

        {/* Active Users */}
        <div className="bg-gradient-to-br from-amber-50 to-amber-100 p-3 sm:p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between mb-2 sm:mb-3">
            <div>
              <p className="text-xs sm:text-sm font-medium text-amber-900/70">Active Users</p>
              <p className="text-lg sm:text-xl lg:text-2xl font-bold text-amber-900 mt-1">{formatNumber(activeUsers)}</p>
            </div>
            <div className="p-2 bg-amber-200/50 rounded-lg">
              <FiUserCheck className="h-4 w-4 sm:h-5 sm:w-5 text-amber-700" />
            </div>
          </div>
          <div className="flex items-center">
            <span className="text-xs text-amber-800">{Math.round((activeUsers / (totalUsers || 1)) * 100)}% of total users</span>
          </div>
        </div>
      </div>

      {/* Secondary Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-4 sm:mb-6">
        {/* Average Bookings */}
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs font-medium text-gray-600">Avg. Bookings/User</p>
              <p className="text-sm sm:text-base lg:text-lg font-bold text-gray-900 mt-1">{Number(averageBookingsPerUser || 0).toFixed(1)}</p>
              <p className="text-xs text-gray-500 mt-1">bookings per user</p>
            </div>
            <div className="p-2 bg-purple-100 rounded-lg">
              <FiBarChart className="h-4 w-4 text-purple-600" />
            </div>
          </div>
        </div>

        {/* Daily Active Users */}
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs font-medium text-gray-600">Daily Active Users</p>
              <p className="text-sm sm:text-base lg:text-lg font-bold text-gray-900 mt-1">{formatNumber(userActivity?.daily)}</p>
              <p className="text-xs text-gray-500 mt-1">users today</p>
            </div>
            <div className="p-2 bg-blue-100 rounded-lg">
              <FiCalendar className="h-4 w-4 text-blue-600" />
            </div>
          </div>
        </div>

        {/* Weekly Active Users */}
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs font-medium text-gray-600">Weekly Active Users</p>
              <p className="text-sm sm:text-base lg:text-lg font-bold text-gray-900 mt-1">{formatNumber(userActivity?.weekly)}</p>
              <p className="text-xs text-gray-500 mt-1">users this week</p>
            </div>
            <div className="p-2 bg-green-100 rounded-lg">
              <FiActivity className="h-4 w-4 text-green-600" />
            </div>
          </div>
        </div>

        {/* Monthly Active Users */}
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs font-medium text-gray-600">Monthly Active Users</p>
              <p className="text-sm sm:text-base lg:text-lg font-bold text-gray-900 mt-1">{formatNumber(userActivity?.monthly)}</p>
              <p className="text-xs text-gray-500 mt-1">users this month</p>
            </div>
            <div className="p-2 bg-amber-100 rounded-lg">
              <FiTarget className="h-4 w-4 text-amber-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm">
        <h3 className="text-sm sm:text-base font-semibold text-gray-900 mb-3 sm:mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
          <button 
            onClick={() => window.location.href = '/admin/users'}
            className="group p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg transition-all duration-200 text-left hover:shadow-md"
          >
            <FiUsers className="h-4 w-4 text-blue-600 mb-2 group-hover:scale-110 transition-transform duration-200" />
            <h4 className="text-sm font-medium text-gray-900 mb-0.5">View All Users</h4>
            <p className="text-xs text-gray-600">Manage user accounts</p>
          </button>
          
          <button 
            onClick={() => window.location.href = '/admin/users/suspended'}
            className="group p-3 bg-gradient-to-br from-rose-50 to-rose-100 rounded-lg transition-all duration-200 text-left hover:shadow-md"
          >
            <FiUserX className="h-4 w-4 text-rose-600 mb-2 group-hover:scale-110 transition-transform duration-200" />
            <h4 className="text-sm font-medium text-gray-900 mb-0.5">Suspended Users</h4>
            <p className="text-xs text-gray-600">Review suspended accounts</p>
          </button>
          
          <button 
            onClick={refreshStats}
            disabled={isRefreshing}
            className="group p-3 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg transition-all duration-200 text-left hover:shadow-md disabled:opacity-50"
          >
            <FiRefreshCw className={`h-4 w-4 text-emerald-600 mb-2 group-hover:scale-110 transition-transform duration-200 ${isRefreshing ? 'animate-spin' : ''}`} />
            <h4 className="text-sm font-medium text-gray-900 mb-0.5">Refresh Data</h4>
            <p className="text-xs text-gray-600">Update all statistics</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserStats; 