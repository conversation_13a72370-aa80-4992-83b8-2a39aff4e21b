import React, { useState, useEffect } from 'react';
import { FiCreditCard, FiCalendar, FiCheck, FiX, FiAlertCircle, FiRefreshCw, FiClock, FiDollarSign, FiStar, FiShield, FiZap, FiUsers, FiTrendingUp, FiLoader } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../hooks/useAuth';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { subscriptionService } from '../../services/api';
import api from '../../utils/api';
import LoadingSpinner from '../../components/common/LoadingSpinner';

// Add custom Naira icon component
const NairaIcon = ({ className }) => (
  <svg 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <text x="50%" y="50%" dominantBaseline="middle" textAnchor="middle" fontSize="16" fill="none" stroke="currentColor">₦</text>
  </svg>
);

const BarberSubscription = () => {
  const [subscription, setSubscription] = useState(null);
  const [paymentHistory, setPaymentHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [authError, setAuthError] = useState(false);
  const [daysRemaining, setDaysRemaining] = useState(null);
  const { user, isAuthenticated, token } = useAuth();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // Payment verification states
  const [isVerifyingPayment, setIsVerifyingPayment] = useState(false);
  const [hasShownSuccessToast, setHasShownSuccessToast] = useState(false);
  // Payment verification states removed - functionality disabled

  // Get payment parameters from URL
  const paymentReference = searchParams.get('reference');
  const paymentStatus = searchParams.get('status');

  // Calculate days remaining
  const calculateDaysRemaining = (endDate) => {
    if (!endDate) return 0;
    
    const end = new Date(endDate);
    const now = new Date();
    
    // Reset time part to compare dates only
    end.setHours(0, 0, 0, 0);
    now.setHours(0, 0, 0, 0);
    
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays); // Don't return negative days
  };

  // Update days remaining
  useEffect(() => {
    if (subscription?.currentPeriod?.endDate) {
      const updateDaysRemaining = () => {
        const days = calculateDaysRemaining(subscription.currentPeriod.endDate);
        setDaysRemaining(days);
      };

      // Update immediately
      updateDaysRemaining();

      // Update at midnight
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      const timeUntilMidnight = tomorrow.getTime() - now.getTime();

      // Set timeout for first update at midnight
      const midnightTimeout = setTimeout(() => {
        updateDaysRemaining();
        // Then set interval for subsequent days
        const dailyInterval = setInterval(updateDaysRemaining, 24 * 60 * 60 * 1000);
        return () => clearInterval(dailyInterval);
      }, timeUntilMidnight);

      return () => clearTimeout(midnightTimeout);
    }
  }, [subscription?.currentPeriod?.endDate]);

  const fetchSubscriptionData = async () => {
    try {
      setAuthError(false);
      const response = await subscriptionService.getSubscriptionStatus();
      
      if (response.data?.subscription) {
        setSubscription(response.data.subscription);
      } else if (response.data?.data?.subscription) {
        setSubscription(response.data.data.subscription);
      } else {
        setSubscription({
          status: 'inactive',
          isActive: false,
          daysRemaining: 0
        });
      }
      setLoading(false);
    } catch (error) {
      if (error.response?.status === 401) {
        setAuthError(true);
      }
      setLoading(false);
    }
  };

  const fetchPaymentHistory = async () => {
    try {
      const response = await subscriptionService.getPaymentHistory(5);
      if (response.data.success) {
        setPaymentHistory(response.data.data.payments);
      }
    } catch (error) {
      console.error('Error fetching payment history:', error);
      
      // Don't show error for payment history as it's not critical
      if (error.response?.status !== 401) {
        setPaymentHistory([]);
      }
    }
  };

  // Payment verification functions removed - functionality disabled

      // Clean up verification attempt flag
      const verificationKey = `verification_attempted_${referenceId}`;
      
      if (response.success && (response.status === 'success' || response.status === 'completed')) {
        console.log('SUCCESS CONDITION: Payment successful');
        alert('SUCCESS: Payment successful!');
        const { subscription: verifiedSubscription, barber, payment } = response.data;

        setIsVerifyingPayment(false);

        // Payment successful - show toast only once using sessionStorage
        const toastKey = `subscription_success_${referenceId}`;
        const hasShownToast = sessionStorage.getItem(toastKey);
        
        if (!hasShownToast && !hasShownSuccessToast) {
          setHasShownSuccessToast(true);
          sessionStorage.setItem(toastKey, 'true');
          
          toast.dismiss();
          
          toast.success(
            `🎉 Subscription activated! Your profile can now be made visible to customers.`,
            {
              duration: 4000,
              position: 'top-center',
            }
          );
        }
        
        // Refresh subscription data
        await fetchSubscriptionData();
        
        // Clean URL parameters and redirect after showing toast
        setTimeout(() => {
          sessionStorage.removeItem(verificationKey);
          sessionStorage.removeItem('payment_initiated');
          navigate('/barber/subscription', { replace: true });
        }, 2000);
        
      } else if (response.status === 'failed') {
        console.log('FAILED CONDITION: Payment failed');
        setIsVerifyingPayment(false);
        toast.dismiss();
        toast.error('💳 Payment failed. Please check your card details and try again.');
        sessionStorage.removeItem(verificationKey);
        sessionStorage.removeItem('payment_initiated');
        navigate('/barber/subscription', { replace: true });

      } else if (response.status === 'cancelled') {
        console.log('CANCELLED CONDITION: Payment cancelled');
        alert('CANCELLED: Payment was cancelled!');
        await handlePaymentCancellation();
        
      } else if (response.status === 'pending') {
        console.log('PENDING CONDITION: Payment pending');
        setIsVerifyingPayment(false);
        toast.dismiss();
        toast.error('Payment is still being processed. Please wait a moment and refresh the page.');
        sessionStorage.removeItem(verificationKey);
        navigate('/barber/subscription', { replace: true });

      } else if (response.status === 'error') {
        console.log('ERROR CONDITION: Payment error');
        setIsVerifyingPayment(false);
        toast.dismiss();

        // Show specific error message
        toast.error(response.message || 'Payment verification failed');

        // Remove payment params from URL
        sessionStorage.removeItem(verificationKey);
        navigate('/barber/subscription', { replace: true });

      } else {
        // Unknown status or verification failed
        console.log('UNKNOWN CONDITION: Unknown status:', response.status);
        alert('UNKNOWN: Status = ' + response.status);
        setIsVerifyingPayment(false);
        toast.dismiss();
        toast.error('Payment verification failed. Please contact support if payment was successful.');
        sessionStorage.removeItem(verificationKey);
        navigate('/barber/subscription', { replace: true });
      }
    } catch (error) {
      console.error('VERIFICATION ERROR: Payment verification error:', error);
      console.error('VERIFICATION ERROR: Error response:', error.response?.data);
      console.error('VERIFICATION ERROR: Error status:', error.response?.status);

      // Clean up verification attempt flag
      const verificationKey = `verification_attempted_${referenceId}`;
      sessionStorage.removeItem(verificationKey);

      // Check for authentication errors
      if (error.response?.status === 401) {
        console.log('VERIFICATION ERROR: Authentication error detected');
        setAuthError(true);
        toast.error('Authentication error. Please refresh the page.');
        return;
      }

      // Handle other errors
      console.log('VERIFICATION ERROR: Handling as general error');
      setIsVerifyingPayment(false);
      toast.dismiss();
      toast.error('Failed to verify payment. Please contact support if payment was successful.');
      navigate('/barber/subscription', { replace: true });
    }
  };

  // Verify pending subscription without a specific reference
  const verifyPendingSubscription = async () => {
    try {
      console.log('Attempting to verify pending subscription without reference');
      
      // Call the backend to check for any recent pending transactions
      const response = await subscriptionService.verifyPendingSubscription();
      console.log('Pending subscription verification response:', response);
      
      // Handle the response similar to regular verification
      if (response.success && (response.status === 'success' || response.status === 'completed')) {
        setIsVerifyingPayment(false);
        
        toast.dismiss();
        toast.success(
          `🎉 Subscription activated! Your profile can now be made visible to customers.`,
          {
            duration: 4000,
            position: 'top-center',
          }
        );
        
        // Refresh subscription data
        await fetchSubscriptionData();
        
        // Clean URL parameters
        setTimeout(() => {
          navigate('/barber/subscription', { replace: true });
        }, 2000);
      } else if (response.status === 'failed') {
        setIsVerifyingPayment(false);
        toast.dismiss();
        toast.error('Subscription payment failed. Please try again.');
        navigate('/barber/subscription', { replace: true });
      } else if (response.status === 'cancelled') {
        setIsVerifyingPayment(false);
        toast.dismiss();
        toast.error(response.message || 'Subscription payment was cancelled.');
        navigate('/barber/subscription', { replace: true });
      } else if (response.status === 'pending') {
        setIsVerifyingPayment(false);
        toast.dismiss();
        toast.error('Payment is still being processed. Please wait a moment and refresh the page.');
        navigate('/barber/subscription', { replace: true });
      } else {
        setIsVerifyingPayment(false);
        toast.dismiss();
        toast.error('Payment verification failed. Please contact support if payment was successful.');
        navigate('/barber/subscription', { replace: true });
      }
    } catch (error) {
      console.error('Error verifying pending subscription:', error);
      
      if (error.response?.status === 401) {
        setAuthError(true);
        toast.error('Authentication error. Please refresh the page.');
        return;
      }
      
      setIsVerifyingPayment(false);
      toast.dismiss();
      toast.error('Failed to verify payment. Please contact support if payment was successful.');
      navigate('/barber/subscription', { replace: true });
    }
  };

  const handlePaymentVerification = (reference, source) => {
    console.log(`HANDLE PAYMENT VERIFICATION: Starting from source: ${source} with reference: ${reference}`);
    
    // If subscription is already active, don't attempt verification
    if (subscription.status === 'active') {
      console.log('Subscription already active, skipping payment verification');
      toast.success('Your subscription is already active!', {
        duration: 3000,
        position: 'top-center',
      });
      // Clean URL parameters
      navigate('/barber/subscription', { replace: true });
      return;
    }
    
    // Check if we've already tried to verify this payment
    const verificationKey = reference ? `verification_attempted_${reference}` : `verification_attempted_${source}`;
    const hasAttempted = sessionStorage.getItem(verificationKey);
    
    if (hasAttempted && source !== 'auto_check') {
      console.log('Verification already attempted, cleaning URL');
      navigate('/barber/subscription', { replace: true });
      return;
    }
    
    // Mark as attempted
    sessionStorage.setItem(verificationKey, 'true');
    
    // Only verify if subscription is not active
    setIsVerifyingPayment(true);
    setVerificationStartTime(Date.now()); // Track start time

    // Much faster initial check - if user just closed modal, cancel immediately
    setTimeout(() => {
      if (isVerifyingPayment && !isCancelling && !hasHandledCancellation) {
        checkForModalClosure();
      }
    }, 500); // Reduced from 1000ms to 500ms for faster detection

    // Start verification process
    const timer = setTimeout(() => {
      if (reference) {
        console.log('TIMER: Calling verifySubscriptionPaymentStatus with reference:', reference);
        verifySubscriptionPaymentStatus(reference);
      } else {
        console.log('TIMER: Calling verifyPendingSubscription (no reference)');
        verifyPendingSubscription();
      }
    }, 1000);

    return () => clearTimeout(timer);
  };

  useEffect(() => {
    // Add a small delay to ensure auth state is properly loaded
    const timer = setTimeout(() => {
      if (isAuthenticated && user && user.role === 'barber') {
        fetchSubscriptionData();
        fetchPaymentHistory();
      } else if (!isAuthenticated) {
        setAuthError(true);
        setLoading(false);
      } else {
        setLoading(false);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [isAuthenticated, user]);

  // Detect when user becomes active again (returns from Monnify)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && isVerifyingPayment && paymentReference && !isCancelling && !hasHandledCancellation) {
        // User returned to the page - check if they closed the modal (much faster)
        setTimeout(() => {
          if (isVerifyingPayment && !isCancelling && !hasHandledCancellation) {
            checkForModalClosure();
          }
        }, 300); // Reduced from 500ms to 300ms for faster detection
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isVerifyingPayment, paymentReference, isCancelling, hasHandledCancellation]);

  // Handle URL parameters after subscription data is loaded
  useEffect(() => {
    if (subscription && !loading) {
      const urlParams = new URLSearchParams(window.location.search);
      const status = urlParams.get('status');

      // Look for various reference parameters that Monnify might send
      const reference = urlParams.get('transactionReference') ||
                       urlParams.get('reference') ||
                       urlParams.get('paymentReference') ||
                       urlParams.get('ref') ||
                       urlParams.get('txnRef');

      // Log all URL parameters for debugging
      console.log('URL parameters after redirect:', {
        reference,
        status,
        hasUrlParams: urlParams.toString().length > 0,
        subscriptionStatus: subscription.status,
        allParams: Object.fromEntries(urlParams.entries())
      });

      // Check if this is a legitimate Monnify success redirect
      // Only verify if:
      // 1. We have a reference
      // 2. Status is 'verifying'
      // 3. We have a payment_initiated flag (meaning we actually initiated a payment)
      // 4. The payment was initiated recently (within last 10 minutes to prevent stale URLs)
      const paymentInitiated = sessionStorage.getItem('payment_initiated');
      const isRecentPayment = paymentInitiated && (Date.now() - parseInt(paymentInitiated)) < 10 * 60 * 1000; // 10 minutes

      console.log('Payment verification conditions check:', {
        paymentInitiated,
        paymentInitiatedTime: paymentInitiated ? new Date(parseInt(paymentInitiated)).toISOString() : 'none',
        isRecentPayment,
        timeDiff: paymentInitiated ? (Date.now() - parseInt(paymentInitiated)) / 1000 : 'N/A'
      });

      if (reference && status === 'verifying' && paymentInitiated && isRecentPayment) {
        console.log('Legitimate Monnify redirect detected - verifying payment');
        console.log('Calling handlePaymentVerification with reference:', reference);
        handlePaymentVerification(reference, 'monnify_success_redirect');
        return;
      } else {
        console.log('Redirect conditions not met:', {
          hasReference: !!reference,
          statusIsVerifying: status === 'verifying',
          hasPaymentInitiated: !!paymentInitiated,
          isRecentPayment,
          paymentInitiatedTime: paymentInitiated ? new Date(parseInt(paymentInitiated)).toISOString() : 'none'
        });
      }

      // If we have URL parameters but it's not a legitimate redirect, it might be:
      // - User closed payment and manually navigated back
      // - Stale URL from old payment attempt
      // - User refreshed page with old parameters
      if (reference && status === 'verifying' && (!paymentInitiated || !isRecentPayment)) {
        console.log('Ignoring stale or invalid payment parameters');
        navigate('/barber/subscription', { replace: true });
        return;
      }

      // If we have URL parameters but it's not a legitimate success redirect, clean them up
      if (urlParams.toString().length > 0) {
        console.log('Cleaning up URL parameters (not a legitimate success redirect)');
        navigate('/barber/subscription', { replace: true });
      }

      // Clean up payment session when user returns without success redirect
      if (paymentInitiated && !reference) {
        console.log('User returned without success redirect - cleaning up session');
        sessionStorage.removeItem('payment_initiated');

        // If subscription is still pending, reset it silently
        if (subscription.status === 'pending') {
          console.log('Silently resetting pending subscription');
          subscriptionService.cancelPending()
            .then(() => {
              console.log('Subscription status reset silently');
              fetchSubscriptionData();
            })
            .catch((error) => {
              console.error('Failed to reset subscription status:', error);
            });
        }
      }
    }
  }, [subscription, loading, navigate]);

  const handleSubscriptionPayment = async () => {
    // Payment functionality disabled
    toast.error('Subscription payment is currently disabled. Please contact support.');
  };

  const getSubscriptionStatus = () => {
    if (!subscription) return 'inactive';
    
    // If never subscribed before, show as inactive
    if (subscription.status === 'never_subscribed') {
      return 'inactive';
    }
    
    // Check if subscription has expired based on calculated days
    if (daysRemaining === 0 || subscription.status === 'expired') {
      return 'expired';
    }
    
    // Then check if subscription is active
    if (subscription.isActive && subscription.status === 'active') {
      return 'active';
    }
    
    // If status is explicitly set, use it
    if (subscription.status) {
      return subscription.status;
    }
    
    return 'inactive';
  };

  const getStatusConfig = (status) => {
    const configs = {
      active: {
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        icon: <FiCheck className="h-3.5 w-3.5" />,
        label: 'Active',
        message: `Your subscription is active and your profile is visible to customers.`
      },
      inactive: {
        color: 'text-gray-600',
        bgColor: 'bg-gray-100',
        icon: <FiX className="h-5 w-5" />,
        label: 'Inactive',
        message: 'Your profile is currently not visible to customers. Subscribe now to start receiving bookings.'
      },
      expired: {
        color: 'text-red-600',
        bgColor: 'bg-red-100',
        icon: <FiX className="h-5 w-5" />,
        label: 'Expired',
        message: 'Your subscription has expired. Your profile is currently not visible to customers. Renew now to continue receiving bookings.'
      }
    };
    return configs[status] || configs.inactive;
  };

  const getDaysRemainingColor = (days) => {
    if (days > 7) return 'bg-green-50 text-green-800';
    if (days > 3) return 'bg-yellow-50 text-yellow-800';
    return 'bg-red-50 text-red-800';
  };

  const renderSubscriptionCard = () => {
    const status = getSubscriptionStatus();
    const config = getStatusConfig(status);
    const isActive = status === 'active';
    const isExpired = status === 'expired';
    const isInactive = status === 'inactive';

    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        {/* Card Header - Mobile Optimized */}
        <div className="p-4 sm:p-6 border-b border-gray-100">
          <div className="flex flex-col gap-4">
            <div className="flex items-start justify-between">
              <div>
                <h2 className="text-lg sm:text-xl font-bold text-gray-900">Subscription Status</h2>
                <p className="text-sm text-gray-600 mt-1">Manage your subscription and visibility</p>
              </div>
              <div className={`shrink-0 ${
                isActive 
                  ? 'px-2 py-1 flex items-center gap-1 text-xs font-medium' 
                  : 'px-3 py-1.5 sm:px-4 sm:py-2 flex items-center gap-2 text-sm'
                } rounded-xl ${config.bgColor} ${config.color}`}
              >
                {config.icon}
                {config.label}
              </div>
            </div>
          </div>
        </div>

        {/* Card Content - Mobile Optimized */}
        <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Status Message */}
          <div className="flex items-start gap-3 sm:gap-4">
            <div className={`p-2 sm:p-3 rounded-xl ${config.bgColor} flex-shrink-0`}>
              <FiShield className={`h-5 w-5 sm:h-6 sm:w-6 ${config.color}`} />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-sm sm:text-base font-semibold text-gray-900">Current Status</h3>
              <p className="text-sm text-gray-600 mt-1 break-words">{config.message}</p>
            </div>
          </div>

          {/* Subscription Details - Grid Layout */}
          {subscription && (
            <div className="space-y-4">
              {/* Dates and Fee */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                {/* Start Date */}
                {subscription.currentPeriod?.startDate && (
                  <div className="bg-gray-50 rounded-xl p-3 sm:p-4">
                    <div className="flex items-center gap-3">
                      <div className="shrink-0">
                        <FiCalendar className="h-5 w-5 text-gray-600" />
                </div>
                      <div className="min-w-0">
                        <p className="text-xs sm:text-sm text-gray-600">Start Date</p>
                        <p className="text-sm sm:text-base font-semibold text-gray-900 truncate">
                          {new Date(subscription.currentPeriod.startDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            )}

                {/* End Date */}
                {subscription.currentPeriod?.endDate && (
                  <div className="bg-gray-50 rounded-xl p-3 sm:p-4">
                    <div className="flex items-center gap-3">
                      <div className="shrink-0">
                        <FiClock className="h-5 w-5 text-gray-600" />
                  </div>
                      <div className="min-w-0">
                        <p className="text-xs sm:text-sm text-gray-600">End Date</p>
                        <p className={`text-sm sm:text-base font-semibold truncate ${isExpired ? 'text-red-600' : 'text-gray-900'}`}>
                          {new Date(subscription.currentPeriod.endDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            )}

                {/* Subscription Fee */}
                {subscription.subscriptionFee && (
                  <div className="bg-gray-50 rounded-xl p-3 sm:p-4">
                    <div className="flex items-center gap-3">
                      <div className="shrink-0">
                        <NairaIcon className="h-5 w-5 text-gray-600" />
                  </div>
                      <div className="min-w-0">
                        <p className="text-xs sm:text-sm text-gray-600">Monthly Fee</p>
                        <p className="text-sm sm:text-base font-semibold text-gray-900 truncate">
                          ₦{subscription.subscriptionFee.toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            )}
              </div>

              {/* Simplified Days Remaining Counter */}
              {subscription && daysRemaining !== null && (
                <div className={`rounded-xl p-3 sm:p-4 ${getDaysRemainingColor(daysRemaining)}`}>
                  <div className="flex items-center justify-between gap-3">
                    <div className="flex items-center gap-3">
                      <div className="shrink-0">
                        <FiClock className="h-5 w-5" />
                  </div>
                  <div>
                        <p className="text-sm font-medium">
                          {daysRemaining === 0 ? (
                            'Subscription Expired'
                          ) : (
                            <>
                              {daysRemaining} day{daysRemaining !== 1 ? 's' : ''} remaining
                            </>
                          )}
                    </p>
                  </div>
                </div>
                    {daysRemaining <= 7 && daysRemaining > 0 && (
                      <button
                        onClick={handleSubscriptionPayment}
                        className="text-xs font-medium px-3 py-1.5 rounded-lg bg-white/20 hover:bg-white/30 transition-colors"
                      >
                        Renew Early
                      </button>
                    )}
                  </div>
                </div>
              )}
              </div>
            )}

          {/* Action Button Section */}
          {!isActive && (
            <div className="space-y-4">
              {/* Expired Warning - only show for truly expired subscriptions */}
              {isExpired && (
                <div className="bg-red-50 border border-red-100 rounded-xl p-3 sm:p-4">
                <div className="flex items-start gap-3">
                    <FiAlertCircle className="h-5 w-5 text-red-500 mt-0.5 shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-red-800">
                        Your subscription expired on {subscription?.currentPeriod?.endDate 
                          ? new Date(subscription.currentPeriod.endDate).toLocaleDateString()
                          : 'N/A'}
                      </p>
                      <p className="text-xs text-red-600 mt-1">
                        Renew now to reactivate your profile and continue receiving bookings
                      </p>
                    </div>
                  </div>
                </div>
            )}

              {/* Action Button */}
                    <button
                      onClick={handleSubscriptionPayment}
                      disabled={paymentLoading || isVerifyingPayment}
                className={`w-full px-4 sm:px-6 py-3 rounded-xl font-medium transition-all transform hover:scale-102 shadow-lg disabled:opacity-50 disabled:hover:scale-100 flex items-center justify-center gap-2 text-sm sm:text-base ${
                  isExpired 
                    ? 'bg-red-600 hover:bg-red-700 text-white' 
                    : 'bg-gray-900 hover:bg-gray-800 text-white'
                }`}
                    >
                {paymentLoading || isVerifyingPayment ? (
                        <>
                    <FiLoader className="animate-spin h-4 w-4 sm:h-5 sm:w-5" />
                    <span>Processing...</span>
                        </>
                      ) : (
                        <>
                    <FiCreditCard className="h-4 w-4 sm:h-5 sm:w-5" />
                    <span>{isExpired ? 'Renew Subscription' : 'Subscribe Now'}</span>
                        </>
                      )}
              </button>
            </div>
          )}
        </div>
              </div>
    );
  };

  const renderSubscriptionBenefits = () => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100">
      <div className="p-4 sm:p-6 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-50 rounded-lg">
            <FiStar className="h-5 w-5 text-purple-600" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Subscription Benefits</h2>
            <p className="text-sm text-gray-600">What you get with your subscription</p>
          </div>
        </div>
      </div>

      <div className="p-4 sm:p-6">
        <div className="grid gap-4">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <FiUsers className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900">Profile Visibility</h3>
              <p className="mt-1 text-sm text-gray-600">Your profile becomes visible to potential customers in your area</p>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="p-2 bg-green-50 rounded-lg">
              <FiDollarSign className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900">Receive Bookings</h3>
              <p className="mt-1 text-sm text-gray-600">Accept bookings and payments from customers</p>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="p-2 bg-yellow-50 rounded-lg">
              <FiTrendingUp className="h-4 w-4 text-yellow-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900">Business Growth</h3>
              <p className="mt-1 text-sm text-gray-600">Expand your customer base and grow your business</p>
            </div>
          </div>

          <div className="flex items-start gap-3">
            <div className="p-2 bg-red-50 rounded-lg">
              <FiZap className="h-4 w-4 text-red-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900">Premium Features</h3>
              <p className="mt-1 text-sm text-gray-600">Access to all platform features and tools</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-sm sm:text-base text-gray-600">Loading subscription details...</p>
        </div>
      </div>
    );
  }

  if (authError) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="bg-red-100 p-3 rounded-full inline-flex">
            <FiAlertCircle className="h-6 w-6 text-red-600" />
          </div>
          <h3 className="mt-4 text-lg font-semibold text-gray-900">Authentication Error</h3>
          <p className="mt-2 text-sm text-gray-600">Please refresh the page or log in again.</p>
        </div>
      </div>
    );
  }

  if (isVerifyingPayment) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <h3 className="mt-4 text-lg font-semibold text-gray-900">Verifying Payment</h3>
          <p className="mt-2 text-sm text-gray-600">Please wait while we confirm your payment...</p>
        </div>
      </div>
    );
  }



  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Subscription Management</h1>
          <p className="mt-1 text-sm text-gray-600">Manage your subscription and profile visibility</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {/* Subscription Status Card */}
        {renderSubscriptionCard()}

        {/* Subscription Benefits Card */}
        {renderSubscriptionBenefits()}

        {/* Payment History Section */}
        {paymentHistory.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment History</h2>
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {paymentHistory.map((payment) => (
                      <tr key={payment._id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(payment.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          ₦{payment.amount.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            payment.status === 'completed' ? 'bg-green-100 text-green-800' :
                            payment.status === 'failed' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {payment.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {payment.paymentReference}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BarberSubscription; 